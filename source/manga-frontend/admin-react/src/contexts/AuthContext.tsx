import { createContext, useContext, useState, ReactNode, useEffect } from "react";
import authService from "../services/auth-service";
import userService from "../services/user-service";
import { TOKEN_STORAGE } from "../configurations/api-config";
import { UserResponse } from "../interfaces/models/auth";

interface AuthContextType {
    isLogin: boolean;
    hasMangaManagement: boolean;
    hasSystemManagement: boolean;
    userPermissions: string[];
    user: UserResponse | null;
    userProfile: UserResponse | null;
    login: (authResponse: { token: string, refreshToken: string, expiresIn?: number }) => void;
    logout: () => void;
    refreshUserProfile: () => Promise<void>;
    hasPermission: (permission: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
    const [isLogin, setIsLogin] = useState<boolean>(() => {
        return !!localStorage.getItem(TOKEN_STORAGE.ACCESS_TOKEN);
    });
    const [hasMangaManagement, setHasMangaManagement] = useState<boolean>(false);
    const [hasSystemManagement, setHasSystemManagement] = useState<boolean>(false);
    const [userPermissions, setUserPermissions] = useState<string[]>([]);
    const [user, setUser] = useState<UserResponse | null>(null);
    const [userProfile, setUserProfile] = useState<UserResponse | null>(null);

    // Hàm kiểm tra quyền cụ thể
    const hasPermission = (permission: string): boolean => {
        return userPermissions.includes(permission);
    };

    // Lấy thông tin người dùng khi đã đăng nhập
    useEffect(() => {
        const fetchUserInfo = async () => {
            if (isLogin) {
                // Lấy thông tin cơ bản của người dùng từ token JWT
                const userInfo = authService.getMyInfo();
                if (userInfo) {
                    console.log("AuthContext: Thông tin người dùng từ token:", userInfo);
                    setUser(userInfo);

                    // Kiểm tra quyền từ token
                    const token = localStorage.getItem(TOKEN_STORAGE.ACCESS_TOKEN);
                    if (token) {
                        try {
                            const base64Url = token.split('.')[1];
                            const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                            const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                            }).join(''));

                            const payload = JSON.parse(jsonPayload);

                            // Lấy danh sách quyền từ token
                            const permissions: string[] = payload.scope ? payload.scope.split(' ') : [];
                            setUserPermissions(permissions);

                            // Kiểm tra các quyền cụ thể
                            const hasMangaManagementPerm = permissions.includes('MANGA_MANAGEMENT');
                            const hasSystemManagementPerm = permissions.includes('SYSTEM_MANAGEMENT');

                            // Cập nhật các state quyền
                            setHasMangaManagement(hasMangaManagementPerm);
                            setHasSystemManagement(hasSystemManagementPerm);

                        } catch (error) {
                            console.error("AuthContext: Lỗi khi parse token:", error);
                            setHasMangaManagement(false);
                            setHasSystemManagement(false);
                        }
                    }

                    // Lấy thông tin chi tiết từ API
                    if (userInfo.id) {
                        try {
                            const profileData = await userService.getProfileByUserId(userInfo.id);
                            if (profileData) {
                                console.log("AuthContext: Thông tin chi tiết người dùng từ API:", profileData);
                                setUserProfile(profileData);
                            }
                        } catch (error) {
                            console.error("AuthContext: Lỗi khi lấy thông tin chi tiết người dùng:", error);
                        }
                    }
                }
            }
        };

        fetchUserInfo();
    }, [isLogin]);

    const login = (authResponse: { token: string, refreshToken: string, expiresIn?: number }) => {
        // Lưu token vào localStorage
        localStorage.setItem(TOKEN_STORAGE.ACCESS_TOKEN, authResponse.token);
        localStorage.setItem(TOKEN_STORAGE.REFRESH_TOKEN, authResponse.refreshToken);

        if (authResponse.expiresIn) {
            const expiryTime = Date.now() + authResponse.expiresIn * 1000;
            localStorage.setItem(TOKEN_STORAGE.TOKEN_EXPIRY, expiryTime.toString());
        }

        setIsLogin(true);

        // Lấy thông tin người dùng từ token
        const userInfo = authService.getMyInfo();
        if (userInfo) {
            console.log("AuthContext: Thông tin người dùng từ token:", userInfo);
            setUser(userInfo);

            // Kiểm tra quyền từ token
            try {
                const base64Url = authResponse.token.split('.')[1];
                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                }).join(''));

                const payload = JSON.parse(jsonPayload);

                // Lấy danh sách quyền từ token
                const permissions: string[] = payload.scope ? payload.scope.split(' ') : [];
                setUserPermissions(permissions);

                // Kiểm tra các quyền cụ thể
                const hasMangaManagementPerm = permissions.includes('MANGA_MANAGEMENT');
                const hasSystemManagementPerm = permissions.includes('SYSTEM_MANAGEMENT');

                console.log("AuthContext: Quyền của người dùng:", permissions);
                console.log("AuthContext: Có quyền MANGA_MANAGEMENT:", hasMangaManagementPerm);
                console.log("AuthContext: Có quyền SYSTEM_MANAGEMENT:", hasSystemManagementPerm);

                // Cập nhật các state quyền
                setHasMangaManagement(hasMangaManagementPerm);
                setHasSystemManagement(hasSystemManagementPerm);

            } catch (error) {
                console.error("AuthContext: Lỗi khi parse token:", error);
                setHasMangaManagement(false);
                setHasSystemManagement(false);
            }
        }
    };

    const logout = () => {
        // Xóa tất cả các token khỏi localStorage
        localStorage.removeItem(TOKEN_STORAGE.ACCESS_TOKEN);
        localStorage.removeItem(TOKEN_STORAGE.REFRESH_TOKEN);
        localStorage.removeItem(TOKEN_STORAGE.TOKEN_EXPIRY);

        setIsLogin(false);
        setHasMangaManagement(false);
        setHasSystemManagement(false);
        setUserPermissions([]);
        setUser(null);
        setUserProfile(null);
    };

    const refreshUserProfile = async () => {
        if (isLogin) {
            try {
                const myProfile = await userService.getMyProfile();
                if (myProfile) {
                    console.log("AuthContext: Profile người dùng (refresh):", myProfile);
                    setUserProfile(myProfile);
                }
                return;
            } catch (error) {
                console.error("AuthContext: Lỗi khi refresh profile người dùng:", error);
            }
        }
    };

    return (
        <AuthContext.Provider
            value={{
                isLogin,
                hasMangaManagement,
                hasSystemManagement,
                userPermissions,
                user,
                userProfile,
                login,
                logout,
                refreshUserProfile,
                hasPermission
            }}
        >
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error("useAuth must be used within an AuthProvider");
    }
    return context;
};

